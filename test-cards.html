<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البطاقات العائمة - روما</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .test-container {
            position: relative;
            width: 600px;
            height: 400px;
            background: #333;
            border-radius: 30px;
            overflow: visible;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
        }
        
        .test-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 30px;
        }
        
        .floating-card {
            position: absolute;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            padding: 1.2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.15),
                0 8px 16px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            animation: cardFloat 4s ease-in-out infinite;
            border: 2px solid rgba(212, 175, 55, 0.3);
            transform-origin: center;
            transition: all 0.3s ease;
            cursor: pointer;
            z-index: 10;
        }
        
        .floating-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #D4AF37, #F4D03F, #D4AF37);
            border-radius: 22px;
            z-index: -1;
            opacity: 0.6;
            filter: blur(8px);
        }
        
        .card-1 {
            top: 15%;
            right: -18%;
            animation-delay: 0s;
            transform: rotate(-8deg) scale(0.9);
            transform-origin: left center;
        }

        .card-1::after {
            content: '';
            position: absolute;
            top: 50%;
            left: -12px;
            width: 25px;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.8), transparent);
            transform: translateY(-50%) rotate(-8deg);
            box-shadow: 0 0 8px rgba(212, 175, 55, 0.6);
        }

        .card-2 {
            bottom: 20%;
            left: -20%;
            animation-delay: 1.5s;
            transform: rotate(10deg) scale(0.85);
            transform-origin: right center;
        }

        .card-2::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -12px;
            width: 25px;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.8), transparent);
            transform: translateY(-50%) rotate(10deg);
            box-shadow: 0 0 8px rgba(212, 175, 55, 0.6);
        }

        .card-3 {
            top: 50%;
            right: -22%;
            animation-delay: 3s;
            transform: rotate(-12deg) scale(0.8);
            transform-origin: left center;
        }

        .card-3::after {
            content: '';
            position: absolute;
            top: 50%;
            left: -12px;
            width: 25px;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.8), transparent);
            transform: translateY(-50%) rotate(-12deg);
            box-shadow: 0 0 8px rgba(212, 175, 55, 0.6);
        }

        .card-4 {
            top: 70%;
            left: -18%;
            animation-delay: 4.5s;
            transform: rotate(6deg) scale(0.88);
            transform-origin: right center;
        }

        .card-4::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -12px;
            width: 25px;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.8), transparent);
            transform: translateY(-50%) rotate(6deg);
            box-shadow: 0 0 8px rgba(212, 175, 55, 0.6);
        }
        
        @keyframes cardFloat {
            0%, 100% { 
                transform: translateY(0px) scale(1) rotateZ(0deg);
                box-shadow: 
                    0 20px 40px rgba(0, 0, 0, 0.15),
                    0 8px 16px rgba(0, 0, 0, 0.1),
                    0 0 0 1px rgba(212, 175, 55, 0.1);
            }
            25% {
                transform: translateY(-8px) scale(1.01) rotateZ(1deg);
            }
            50% { 
                transform: translateY(-15px) scale(1.02) rotateZ(0deg);
                box-shadow: 
                    0 35px 70px rgba(0, 0, 0, 0.2),
                    0 15px 30px rgba(0, 0, 0, 0.15),
                    0 0 0 1px rgba(212, 175, 55, 0.2),
                    0 0 20px rgba(212, 175, 55, 0.1);
            }
            75% {
                transform: translateY(-8px) scale(1.01) rotateZ(-1deg);
            }
        }
        
        .card-icon {
            font-size: 1.8rem;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #D4AF37, #F4D03F);
            border-radius: 15px;
            box-shadow: 
                0 8px 16px rgba(212, 175, 55, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.5);
        }
        
        .card-text {
            display: flex;
            flex-direction: column;
            gap: 0.2rem;
        }
        
        .card-title {
            font-size: 1rem;
            font-weight: 700;
            color: #1a1a1a;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        }
        
        .card-subtitle {
            font-size: 0.85rem;
            color: #555;
            font-weight: 500;
        }
        
        .floating-card:hover {
            transform: translateY(-20px) scale(1.1) rotate(0deg) !important;
            box-shadow: 
                0 30px 60px rgba(0, 0, 0, 0.25),
                0 15px 30px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(212, 175, 55, 0.5) !important;
            z-index: 20;
            filter: drop-shadow(0 0 20px rgba(212, 175, 55, 0.4));
        }
        
        .instructions {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h3>اختبار البطاقات العائمة</h3>
        <p>• مرر الماوس فوق البطاقات لرؤية التأثيرات</p>
        <p>• البطاقات تطفو وتتحرك تلقائياً</p>
        <p>• تبدو وكأنها تخرج من الصورة</p>
    </div>
    
    <div class="test-container">
        <img src="/لقطة شاشة 2025-05-27 123414.png" alt="Test Image" class="test-image">
        
        <div class="floating-card card-1">
            <div class="card-icon">⭐</div>
            <div class="card-text">
                <span class="card-title">تقييم ممتاز</span>
                <span class="card-subtitle">4.9/5</span>
            </div>
        </div>
        
        <div class="floating-card card-2">
            <div class="card-icon">🍕</div>
            <div class="card-text">
                <span class="card-title">طبق اليوم</span>
                <span class="card-subtitle">بيتزا روما</span>
            </div>
        </div>
        
        <div class="floating-card card-3">
            <div class="card-icon">☕</div>
            <div class="card-text">
                <span class="card-title">قهوة طازجة</span>
                <span class="card-subtitle">محمصة يومياً</span>
            </div>
        </div>

        <div class="floating-card card-4">
            <div class="card-icon">🎯</div>
            <div class="card-text">
                <span class="card-title">خدمة سريعة</span>
                <span class="card-subtitle">15 دقيقة</span>
            </div>
        </div>
    </div>
</body>
</html>
